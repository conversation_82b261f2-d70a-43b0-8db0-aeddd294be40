import json
from urllib.parse import quote
import re
from bs4 import BeautifulSoup
import random
import time
from datetime import datetime
from fontTools.ttLib import TTFont
from io import BytesIO
import string

from eventlog_spider.common.eventlog_unify import Eventlog, StatusCode
from eventlog_spider.crawler.crawler import <PERSON><PERSON><PERSON>, CrawlerTask, MyException, CrawlerTools
from resx.func import cur_ts_sec
from resx.config import *
from resx.mysql_dao import My<PERSON><PERSON>Dao
from resx.redis_types import RedisQueue
from eventlog_spider.scripts.login import get_cookies
from resx.log import setup_logger
from eventlog_spider.scripts.map_dict import map_dict2

logger = setup_logger(name=__name__)


class CodsCrawlerTask(CrawlerTask):

    def __init__(self, eventlog: Eventlog):
        self.account = {}
        self.cookie = {}
        super().__init__(eventlog)

        if hasattr(eventlog, 'code') and hasattr(eventlog, 'crawler'):
            eventlog.crawler.receive_time = str(cur_ts_sec())


class CodsCrawler(<PERSON><PERSON><PERSON>, <PERSON><PERSON>lerTools):

    @classmethod
    def get_name(cls):
        return 'cods'

    def __init__(self, **kwargs):
        self.headers = {
            "Origin": "https://www.cods.org.cn",
            "Referer": "https://www.cods.org.cn/gscx/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/********* Safari/537.36",
        }
        self.headers2 = {
            "Origin": "https://www.cods.org.cn",
            "Referer": "https://ss.cods.org.cn/isearch",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/********* Safari/537.36",
        }

        self.timeout = 15
        self.account_dao = AccountDao(db_tb_name='prism1.cods_account', **CFG_MYSQL_ZX_RDS113)
        self.account_dao.init_use()
        self.cods_dao = MySQLDao(db_tb_name='internal.cods_company', **CFG_MYSQL_GS_INNER)
        self.url_searchR = "https://ss.cods.org.cn/latest/searchR"
        self.url_detail = "https://ss.cods.org.cn/latest/detail"
        super().__init__(input_queue=RedisQueue(name='cods_new', **CFG_REDIS_GS, db=9), task_cls=CodsCrawlerTask, eventlog_class=Eventlog,
                         **kwargs)

    def do_crawl(self):
        task: CodsCrawlerTask = self.get_crawler_task()
        eventlog: Eventlog = task.eventlog

        if task.keyword is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.code = StatusCode.GIVE_UP
            task.pages = {}
            return

        try:
            task.pages = self.crawl_(task)
            eventlog.code = StatusCode.SUCCESS
        except MyException as e:
            if e.message == '搜索为空':
                logger.warning(f'{task.keyword} -->搜索为空')
                eventlog.code = StatusCode.SEARCH_EMPTY_NO_RETRY
            if e.message == '网站不可查询':
                logger.warning(f'{task.keyword} -->网站不可查询')
                eventlog.code = StatusCode.GENERAL_ERROR_RETRY
                time.sleep(60 * 30)
            if '连续失败' in e.message:
                logger.warning(f'连续失败 {eventlog}')
                eventlog.code = StatusCode.GENERAL_ERROR_RETRY
            task.pages = {}
            return
        except Exception as e:
            raise e

    def crawl_(self, task: CodsCrawlerTask):
        pages = dict()
        info = self.search(task)
        pages['info.txt'] = json.dumps(info, ensure_ascii=False)
        return pages

    def get_cookies(self, task: CodsCrawlerTask):
        for _ in range(20):
            try:
                valid_cookies = self.account_dao.get_valid_cookies()
                # valid_cookies = None
                if valid_cookies:
                    account_cookie = random.choice(valid_cookies)
                    self.initialize_cookie(task, account_cookie, True)
                    return
                else:
                    all_accounts = self.account_dao.get_invalid_cookie_accounts()
                    if not all_accounts:
                        logger.warning(f'当前没有账号能使用')
                        time.sleep(60 * 10)
                        continue
                    invalid_cookie_account = random.choice(all_accounts)
                    self.initialize_cookie(task, invalid_cookie_account)
                    for __ in range(5):
                        # code, task.cookie = get_cookies2(task.account['phone'], task.account['password'])
                        code, task.cookie = get_cookies(task.account['phone'], task.account['password'])
                        if code in ['2']:  # todo 3 验证失败 1 密码错误(网站有问题) 2 账号被停用
                            logger.error(f'账号被停用或密码错误-->{task.account["phone"]}-->{task.account["state"]}-->6')
                            self.account_dao.change_fields(task.account['phone'], ['state'], [6])
                            break
                        elif code in ['-1', '3', '1'] or not task.cookie:  # or not task.cookie.get('IAM_SID')
                            logger.warning(f'登陆失败 code：{code} 重新登陆: {__ + 1}')
                            continue
                        self.account_dao.change_fields(task.account['phone'], ['last_login_time'], [datetime.now()])
                        self.account_dao.change_fields(task.account['phone'], ['cookie', 'update_time'],
                                                       [json.dumps(task.cookie, ensure_ascii=False), datetime.now()])
                        return
                    self.cancel_use(task, True)
            except Exception as e:
                logger.warning(f'获取cookies失败 重试 {e}')
                self.cancel_use(task, True)
        raise MyException('连续失败')

    def initialize_cookie(self, task: CodsCrawlerTask, account_cookie, if_cookie=False):
        self.account_dao.change_fields(account_cookie['phone'], ['is_login'], [1])
        self.account_dao.change_fields(account_cookie['phone'], ['update_time'], [datetime.now()])
        task.account = {'phone': account_cookie['phone'], 'password': account_cookie['password'], 'state': account_cookie['state']}
        logger.info(f'使用账号-->{task.account["phone"]}-->{task.account["password"]}')
        if if_cookie:
            task.cookie = json.loads(account_cookie['cookie'])

    def cancel_use(self, task: CodsCrawlerTask, if_reset_cookie=False):
        self.account_dao.change_fields(task.account['phone'], ['is_login'], [0])
        if if_reset_cookie:
            self.account_dao.change_fields(task.account['phone'], ['cookie'], [''])
        task.cookie = {}
        task.account = {}

    def set_cookies(self, task: CodsCrawlerTask):
        if not task.cookie:
            self.get_cookies(task)
        task.session.cookies.clear()
        task.session.cookies.update(task.cookie)

    def count_search_time(self, task: CodsCrawlerTask):
        self.account_dao.add_one_search(task.account['phone'])

    def count_detail_time(self, task: CodsCrawlerTask):
        self.account_dao.add_one_detail(task.account['phone'])

    def search(self, task: CodsCrawlerTask):
        params = {"q": quote(task.keyword), "t": "common", "currentPage": "1", "searchToken": ""}
        data = {}
        for _ in range(5):
            jgdm = None
            if_cookie = True
            try:
                self.set_cookies(task)
                organization = self.cods_dao.get(property1=task.keyword)
                organization = organization if organization else self.cods_dao.get(name=task.keyword)
                if not organization or not organization['property4']:
                    response = self.request(task, 'get', self.url_searchR, params=params, name=f'searchR - {task.keyword}')
                    if '系统识别存在违规操作，账户已被封禁' in response:
                        logger.error(f'账号被封-->{task.account["phone"]}-->{task.account["state"]}-->6')
                        self.account_dao.change_fields(task.account['phone'], ['state'], [6])
                        continue
                    # if '当前系统访问量大' in response:
                    #     raise MyException('网站不可查询')
                    if '暂无搜索数据' in response:
                        raise MyException('搜索为空')
                    jgdm = re.search(r'detail\?jgdm=(.*?)">', response)
                    if not jgdm:
                        logger.warning('cookies失效-->searchR')
                        continue
                    self.count_search_time(task)
                    logger.info(f'jgdm: {jgdm}')
                jgdm = jgdm.group(1) if jgdm else organization['property4']
                response = self.request(task, 'get', self.url_detail, params={"jgdm": jgdm}, name='cods-detail')
                data = self.parse_html(task, response)
                if not data:
                    logger.warning('cookies失效-->detail')
                    continue
                self.count_detail_time(task)
                data['jgdm'] = jgdm
                if_cookie = False
                break
            except MyException as e:
                if e.message in ['搜索为空', '网站不可查询']:
                    raise e
                logger.warning(f'cookies失效-->{e.message}')
            except Exception as e:
                logger.warning(f'搜索失效-->{e}')
            finally:
                self.cancel_use(task, if_cookie)
        if not data:
            raise MyException('连续失败')
        logger.info(data)
        return data

    def parse_html(self, task: CodsCrawlerTask, html):
        try:
            url = re.search(r"url\('/css/woff/(\d+).woff2'\)", html).group(1)
            url = f"https://ss.cods.org.cn/css/woff/{url}.woff2"
            res = self.request(task, 'GET', url, name='woff2', toRaw=True)
            font = TTFont(BytesIO(res.content))

            map_dict = {}
            digits_and_uppercase = string.digits + string.ascii_uppercase
            map_list = font.getBestCmap()
            for index, (key, value) in enumerate(list(map_list.items())[36:]):
                map_dict[self.hex_to_char(hex(key))] = digits_and_uppercase[index]

            soup = BeautifulSoup(html, 'lxml')
            uls = soup.find_all('ul')
            keys = []
            values = []
            for ul in [uls[3], uls[4], uls[5]]:
                for li in ul.find_all('li'):
                    key_value = list(li.stripped_strings)[:2]
                    if key_value:
                        keys.append(key_value[0])
                        values.append(key_value[1] if len(key_value) > 1 else '')

            keys.append('status')
            status = soup.select('em.tag.green')
            status2 = soup.select('em.tag.red')
            values.append(status[0].text[3:] if status else (status2[0].text[3:] if status2 else '其他'))

            keys.append('最后更新日期')
            date = soup.select('em.tag.blue')
            values.append(date[0].text[-10:] if date else '0001-01-01')

            keys.append('name')
            values.append(list(soup.select_one('div.summary_info>h3').stripped_strings)[0])
            data = dict(zip(keys, values))

            for k, v, in data.items():
                data[k] = self.map_str(v, map_dict)

            return data
        except Exception as e:
            return {}

    @staticmethod
    def hex_to_char(hex_string):
        hex_value = int(hex_string, 16)
        character = chr(hex_value)
        return character

    @staticmethod
    def map_str(s, map_dict: dict = None):
        map_dict.update(map_dict2)
        return ''.join([map_dict.get(i, i) for i in s])


class AccountDao(MySQLDao):

    def get_valid_cookies(self):
        sql = (f"select * from {self.db_tb_name} where cookie <> '' and timestampdiff(MINUTE, update_time, NOW()) > 4 "
               f"and search < 30 and  detail < 60 and is_login = 0 and state = 0")
        return list(self.select_many(sql))

    def get_invalid_cookie_accounts(self):
        sql = (f"select * from {self.db_tb_name} where cookie = '' "
               f"and is_login = 0 and search < 30 and detail < 60 and state = 0 and TIMESTAMPDIFF(MINUTE, last_login_time, NOW()) > 40")
        return list(self.select_many(sql))

    def change_search_detail(self):
        sql = f"update {self.db_tb_name}  set search = 0, detail = 0, is_login = 0 where state = 0"
        return self.execute(sql)

    def add_one_search(self, phone):
        sql = f"update {self.db_tb_name} set search = search + 1 where phone = %s"
        return self.execute(sql, args=[phone])

    def add_one_detail(self, phone):
        sql = f"update {self.db_tb_name} set detail = detail + 1 where phone = %s"
        return self.execute(sql, args=[phone])

    def change_fields(self, phone, fields, values):
        sets = ', '.join(f'{field} = %s' for field in fields)
        sql = f"update {self.db_tb_name} set {sets} where phone = %s"
        return self.execute(sql, args=(*values, phone,))

    def init_use(self):
        sql = 'update prism1.cods_account set is_login=0 where is_login = 1 and state = 0'
        return self.execute(sql)
