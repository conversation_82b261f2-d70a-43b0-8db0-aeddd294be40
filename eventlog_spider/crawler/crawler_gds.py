import re
import requests
import urllib3
import time
import json
import asyncio
import sys
import base64
import random
from typing import Union
import aiohttp

from eventlog_spider.common.eventlog_unify import Eventlog, StatusCode
from eventlog_spider.crawler.crawler import Crawler, CrawlerTask, MyException, CrawlerTools
from resx.func import cur_ts_sec
from resx.config import *
from resx.mysql_dao import MySQLDao
from resx.redis_types import RedisQueue
from resx.log import setup_logger
from eventlog_spider.scripts.gds_login import gds_login

urllib3.disable_warnings()
logger = setup_logger(name=__name__)

if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())


class GdsCrawlerTask(CrawlerTask):

    def __init__(self, eventlog: Eventlog):
        super().__init__(eventlog)
        self.cid = eventlog.selector.info['company_id']
        self.firm_name = eventlog.selector.info['company_name']
        self.session.proxies = CrawlerTools.get_long_proxy()

        if hasattr(eventlog, 'code') and hasattr(eventlog, 'crawler'):
            eventlog.crawler.receive_time = str(cur_ts_sec())


class GdsCrawler(Crawler):
    @classmethod
    def get_name(cls):
        return 'gds'

    def __init__(self, **kwargs):
        self.headers = {
            "Origin": "https://www.gds.org.cn",
            "Referer": "https://www.gds.org.cn/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/119.0.0.0 Safari/537.36",
        }
        self.headers2 = {
            "Origin": "https://www.gds.org.cn",
            "Referer": "https://www.gds.org.cn/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/119.0.0.0 Safari/537.36",
        }
        self.proxies = {
            'http': 'http://10.99.138.95:30636',
            'https': 'http://10.99.138.95:30636'
        }
        self.firm_dao = MySQLDao(**CFG_MYSQL_GS_INNER, db_tb_name='prism.gds_firm')
        self.brand_dao = MySQLDao(**CFG_MYSQL_GS_INNER, db_tb_name='prism.gds_brand')
        self.product_dao = MySQLDao(**CFG_MYSQL_GS_INNER, db_tb_name='prism.gds_product')
        self.timeout = 5
        self.phones = [
            '***********', '***********', '***********', '***********', '***********',
            '***********', '***********', '***********', '***********', '***********', '***********'
        ]
        self.account_manager = {
            phone: {
                'authorization': None,  # 存储Authorization token
                'is_logging': False,  # 是否正在登录
                'login_lock': None,  # 每个账号的登录锁
                'last_login_time': 0,  # 最后登录时间
                'search_count': 0,  # 搜索次数计数
                'daily_search_count': 0,  # 每日搜索次数
                'last_reset_date': time.strftime('%Y-%m-%d')  # 最后重置日期
            } for phone in self.phones
        }
        super().__init__(input_queue=RedisQueue(name='gds_company', **CFG_REDIS_GS, db=9), task_cls=GdsCrawlerTask, eventlog_class=Eventlog,
                         **kwargs)

    def do_crawl(self):
        task: GdsCrawlerTask = self.get_crawler_task()
        eventlog: Eventlog = task.eventlog

        if task.keyword is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.code = StatusCode.GIVE_UP
            task.pages = {}
            return

        try:
            task.pages = self.crawl_(task)
            eventlog.code = StatusCode.SUCCESS
        except MyException as e:
            if e.message == '搜索为空':
                logger.warning(f'{task.keyword} -->搜索为空')
                eventlog.code = StatusCode.SEARCH_EMPTY_NO_RETRY
            if e.message == '403':
                logger.warning(f'{task.keyword} -->403 ip被封重试 {base64.b64decode(task.session.cookies.get("proxyBase", "")).decode()}')
                eventlog.code = StatusCode.GENERAL_ERROR_RETRY
            if e.message in ['206:Failed To Get Valid Ip', '异常IP', '参数解密异常']:
                logger.warning(f'{task.keyword} {eventlog.selector.info["cid"]} {eventlog.selector.word} --> {e.message} 放弃')
                eventlog.code = StatusCode.GENERAL_ERROR_RETRY
            if '连续失败' in e.message:
                logger.warning(f'{task.keyword} -->{e.message}')
                eventlog.code = StatusCode.GENERAL_ERROR_RETRY
            task.pages = {}
        except Exception as e:
            raise e

    def crawl_(self, task: GdsCrawlerTask):
        pages = dict()
        self.login()
        f_id, company_info, fid_old = self.start_search(task)
        barcodes = self.get_barcodes(task, f_id)
        brand_info: list = self.get_brand_list(task, f_id)['Data']['Items']

        brands_old = self.get_brand_from_db(company_info['fid'])
        barcodes_old = self.get_old_barcodes_from_db(company_info['fid'])

        barcodes_by_brand = []
        brands_del = []
        if brand_info:
            barcodes_by_brand, brand_id_count_barcodes, crawled_brands = self.get_barcodes_ByBrand(task, brand_info, brands_old)
            for idx, i in enumerate(brand_info):
                brand_name = i["brandcn"]
                if brand_name not in crawled_brands:
                    brands_del.append(brand_name)
                    continue
                # 更新brand id
                i['brand_id'] = brand_id_count_barcodes.get(f'{brand_name}_id', 0)

        barcodes.extend(barcodes_by_brand)
        barcodes = CrawlerTools.deduplicate_by_field(barcodes, 'gtin')
        company_info["barcodes"] = [i['gtin'] for i in barcodes]

        # 爬取所有barcode
        products_info = self.get_products_info(task, barcodes, barcodes_old)

        # 删除没有商品的品牌
        brand_info = [brand for brand in brand_info if brand['brandcn'] in brands_del]
        company_info['fid_old'] = fid_old
        pages['company_info.txt'] = json.dumps(company_info, ensure_ascii=False)
        pages['brand_info.txt'] = json.dumps(brand_info, ensure_ascii=False)
        pages['products_info.txt'] = json.dumps(products_info, ensure_ascii=False)
        return pages

    def login(self):
        phone = self.phones[random.randint(0, len(self.phones) - 1)]
        tokens = gds_login(username=phone)
        phone_info: dict = self.account_manager[phone]
        phone_info['authorization'] = f"Bearer {tokens['access_token']}"
        self.headers.update({"Authorization": f"Bearer {tokens['access_token']}"})

    def search(self, task, barcode: str) -> dict:
        url = "https://bff.gds.org.cn/gds/searching-api/ProductService/ProductListByGTIN"
        params = {"PageSize": "30", "PageIndex": "1", "SearchItem": barcode}
        response = None
        for i in range(5):
            try:
                response = task.session.get(url, headers=self.headers, params=params, verify=False, timeout=self.timeout)
            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout):
                del task.session.cookies['proxyBase']
                continue
            status = response.status_code
            if status == 200:
                if response.json()['Msg'] == 'Success':
                    break
            else:
                del task.session.cookies['proxyBase']

        if response.status_code == 403:
            raise MyException('403')
        if not response.json()['Data']['Items']:
            raise MyException(f'国外或没有barcode({barcode})')

        logger.info(response.json()['Data']['Items'][0]['f_id'])
        time.sleep(random.randint(1, 2))
        return response.json()['Data']['Items'][0]

    def start_search(self, task: GdsCrawlerTask):
        url = "https://bff.gds.org.cn/gds/searching-api/FirmService/FirmList"
        params = {"PageSize": "10", "PageIndex": "1", "FirmName": task.firm_name}
        res: dict = self.request(task, 'GET', url, params=params, name='search_firm', tojson=True)

        list_ = res['Data']['Items']
        fid = ''
        fid_old = []
        for l in list_:
            company_name, loginout = l['firmname'], l['loginout']
            if re.sub(r'[（）()]', '', company_name) == re.sub(r'[()（）]', '', task.firm_name) and loginout == 1:
                fid_old.append(l['fid'])
            if re.sub(r'[（）()]', '', company_name) == re.sub(r'[()（）]', '', task.firm_name) and loginout == 0:
                fid = l['fid']
        if not fid:
            raise MyException('搜索为空')

        company_info = self.get_company_info(task, fid)
        return fid, company_info, fid_old

    def get_barcodes_ByBrand(self, task: GdsCrawlerTask, brands, brands_old) -> (list, dict, list, list):
        barcodes, brand_id_count_barcodes, crawled_brands, temp = [], {}, [], {}
        for brand in brands:
            brand_name = brand.get('brandcn', '')
            if brand_name == '' or not brand.get('itemcount', 0):
                continue
            url2 = "https://bff.gds.org.cn/gds/searching-api/ProductService/ProductListByBrandNameFID"
            params2 = {"PageSize": "200", "PageIndex": "1", "SearchItem": brand_name, "FId": brand['fid']}
            try:
                response2: dict = self.request(task, 'GET', url2, params=params2, name='get_products_ByBrand_retail', tojson=True)
            except MyException:
                break
            items = response2['Data']['Items']
            if not items:
                brand_id_count_barcodes.update({f'{brand_name}_id': 0, f'{brand_name}_barcodes': []})
                crawled_brands.append(brand_name)
                continue
            temp.update({f'{brand_name}': items})
            time.sleep(random.randint(1, 2))

        for brand_name, items in temp.items():
            barcodes_Bybrand = [item['gtin'] for item in items]  # 总
            brand_id = 0
            if brand_name not in brands_old:
                for i in items:
                    product_info = self.get_product_info(task, i)
                    if product_info:
                        brand_id = product_info[0]['ProductDetailsViewInfoNationalList'][0]['BrandID']
                        break
            else:
                brand_id = brands_old[f'{brand_name}_id']
            barcodes.extend(items)
            brand_id_count_barcodes.update({f'{brand_name}_id': brand_id, f'{brand_name}_barcodes': barcodes_Bybrand})
            crawled_brands.append(brand_name)

        logger.info(barcodes)
        return barcodes, brand_id_count_barcodes, crawled_brands

    def get_products_info(self, task: GdsCrawlerTask, items, barcodes_old) -> list:
        barcodes_new = [item['gtin'] for item in items if item['gtin'] not in barcodes_old]
        if not barcodes_new:
            raise MyException('无新条形码')
        return self.search_products(barcodes_new[:300])  # 限制100个条形码

        # products_info = []
        # product_count = 0
        # fail_count = 0
        # for item in items:
        #     if item['gtin'] in barcodes_old:
        #         continue
        #     try:
        #         product_info = self.get_product_info(task, item)
        #         if product_info:
        #             products_info.append(product_info[0]['ProductDetailsViewInfoNationalList'][0])
        #             product_count += 1
        #         if product_count >= 300:
        #             break
        #         # time.sleep(0.5)
        #     except Exception as e:
        #         fail_count += 1
        #         if fail_count >= 3:
        #             logger.warning(f'{task.keyword} -->{e} 商品详细异常结束 已爬取{product_count}个')
        #             break
        # return products_info

    def get_product_info(self, task, item) -> dict:
        url = "https://bff.gds.org.cn/gds/searching-api/ProductService/ProductInfoByGTIN"
        params = {"gtin": item['gtin'], "id": item['base_id']}
        response: dict = self.request(task, 'GET', url, params=params, name='get_product_info', headers=self.headers2, tojson=True)
        return response['Data']['Items']

    def get_brand_list(self, task: GdsCrawlerTask, f_id) -> dict:
        url = "https://bff.gds.org.cn/gds/searching-api/Brand/GetPagedFirmBrand"
        params = {"PageSize": "10000", "PageIndex": "1", "FId": f_id}
        result: dict = self.request(task, 'GET', url, params=params, name='get_brand_list', tojson=True)
        return result

    def get_barcodes(self, task: GdsCrawlerTask, id_) -> list:
        url = "https://bff.gds.org.cn/gds/searching-api/ProductService/ProductListByFID"
        params = {"PageSize": "100000", "PageIndex": 1, "SearchItem": id_}
        response: dict = self.request(task, 'GET', url, params=params, name='get_barcodes', tojson=True)
        return response['Data']['Items']

    def get_company_info(self, task: GdsCrawlerTask, f_id) -> dict:
        url = "https://bff.gds.org.cn/gds/searching-api/FirmService/FirmInfo"
        params = {"PageSize": "24", "PageIndex": "1", "Fid": f_id}
        response: dict = self.request(task, 'GET', url, params=params, name='get_company_info', tojson=True)
        return response['Data']['Items'][0]

    def search_products(self, barcodes: list) -> list:
        async def main(list_):
            # 在开始搜索前初始化所有账号
            await self.initialize_all_accounts()

            data, barcodes_list, error_count = [], list_, {}
            while barcodes_list:
                barcodes_ = barcodes_list[:15]
                barcodes_list = barcodes_list[15:]
                tasks = [asyncio.create_task(self.search_retail(barcode), name=f"{barcode}") for barcode in barcodes_]
                done, _ = await asyncio.wait(tasks, return_when=asyncio.ALL_COMPLETED)
                for task in done:
                    if task.exception() is None:
                        if not task.result():
                            continue
                        data.append(task.result())
                    else:
                        barcode_failed = task.get_name()
                        barcodes_list.append(barcode_failed)
                        error_count[barcode_failed] = error_count.get(barcode_failed, 0) + 1
                        if error_count[barcode_failed] > 5:
                            logger.warning(f'barcode网站接口搜索有问题 {barcode_failed} 重试次数超过5次')
                            return data
                        logger.warning(f"barcode：{barcode_failed} failed 添加到数组 {task.exception()}")

            # 输出搜索统计信息
            stats = self.get_search_statistics()
            logger.info(f"搜索统计: {stats}")
            logger.info(f"len: {len(data)}")
            return data

        results = asyncio.run(main(barcodes))
        return results

    async def search_retail(self, barcode):
        # 获取当前可用的手机号和token
        phone = self.get_least_used_phone()
        auth_token = await self.get_authorization_for_phone(phone)
        headers = {
            "Origin": "https://www.gds.org.cn",
            "Referer": "https://www.gds.org.cn/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/119.0.0.0 Safari/537.36",
            "Authorization": auth_token  # 动态添加Authorization
        }
        self.increment_search_count(phone)

        for i in range(3):
            try:
                connector = aiohttp.TCPConnector(ssl=False)
                timeout = aiohttp.ClientTimeout(total=self.timeout)
                proxy = self.proxies['http']
                async with aiohttp.ClientSession(connector=connector) as session:
                    url = "https://bff.gds.org.cn/gds/searching-api/ProductService/ProductListByGTIN"
                    params = {"PageSize": "30", "PageIndex": "1", "SearchItem": barcode}
                    response = await session.get(url, headers=headers, params=params, timeout=timeout, proxy=proxy)
                    status = response.status

                    template = "barcode:%s reason:{} satus:%s res:%s" % (barcode, status, await response.text())

                    # 在403错误处理中刷新token
                    if status == 403:
                        status_ = await self.remove_403_async(session, headers)
                        if not status_:
                            # 刷新当前手机号的token
                            auth_token = await self.refresh_authorization_for_phone(phone)
                            headers["Authorization"] = auth_token
                        continue

                    if status == 429:
                        await asyncio.sleep(3)
                        continue

                    if status != 200:
                        logger.error(template.format('error'))
                        raise MyException(f'{status}')

                    if not (await response.json())['Data']['Items']:
                        logger.warning(f"国外或没有barcode: {barcode}")
                        return {}

                    data: dict = (await response.json())['Data']['Items'][0]
                    base_id = data['base_id']

                    # 注册
                    url_ = "https://bff.gds.org.cn/gds/searching-api/ProductService/ProductSimpleInfoByGTIN"
                    response = await session.get(url_, params={"gtin": barcode, "id": base_id}, headers=headers, timeout=timeout, proxy=proxy)
                    await asyncio.sleep(random.randint(1, 2) / 2)

                    url2 = "https://bff.gds.org.cn/gds/searching-api/ProductService/ProductInfoByGTIN"
                    response = await session.get(url2, params={"gtin": barcode, "id": base_id}, headers=headers, timeout=timeout, proxy=proxy)

                    if not (await response.json())['Data']['Items']:
                        return {}
                    data.update((await response.json())['Data']['Items'][0]['ProductDetailsViewInfoNationalList'][0])
                    logger.info(data)
                    return data
            except (TypeError, KeyError, aiohttp.client_exceptions.ContentTypeError) as e:
                logger.warning(f'barcode:{barcode} {e.__class__.__name__} 状态码: {response.status} res: {await response.text()}')
                raise MyException(f'{e.__class__.__name__}')
            except (aiohttp.client_exceptions.ClientHttpProxyError, aiohttp.client_exceptions.ServerDisconnectedError,
                    asyncio.exceptions.TimeoutError, aiohttp.client_exceptions.ClientConnectorError) as e:
                logger.warning(f'barcode:{barcode} 商品详细任务 代理问题:{e.__class__.__name__} 重试')
                raise MyException(f'{e.__class__.__name__} 重试')
            except MyException as e:
                raise e
            except Exception as e:
                response and logger.warning(f'barcode:{barcode} Exception 状态码: {response.status} res: {await response.text()}')
                response and response.status != 403 and logger.warning(f"barcode:{barcode} 商品详细任务 未知错误 {CrawlerTools.custom_traceback(e)}")
                raise e
        raise Exception('超过3次')

    async def initialize_all_accounts(self):
        """初始化所有手机号的登录凭证"""

        async def login_account(phone):
            try:
                account_info = self.account_manager[phone]
                if not account_info['authorization']:
                    await self.get_authorization_for_phone(phone)
                    logger.info(f"手机号 {phone} 初始化登录成功")
            except Exception as e:
                logger.error(f"手机号 {phone} 初始化登录失败: {e}")

        # 并发初始化所有账号
        tasks = [login_account(phone) for phone in self.phones][:5]
        await asyncio.gather(*tasks, return_exceptions=True)
        logger.info("所有账号初始化完成")

    async def remove_403_async(self, session, headers=None):
        """协程版本的remove_403方法"""
        url = "https://bff.gds.org.cn/gds/carding-api/Cards/GetUserIsAuth"
        try:
            async with session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=self.timeout)) as response:
                result = json.loads(await response.text())
                if result.get('Msg') == '操作成功':
                    return True
                return False
        except Exception as e:
            logger.error(f"remove_403_async 异常: {e}")
            return False

    def remove_403(self, session, headers=None):
        url = "https://bff.gds.org.cn/gds/carding-api/Cards/GetUserIsAuth"
        response = session.get(url, headers=self.headers if not headers else headers, verify=False, timeout=self.timeout)
        if response.json()['Msg'] == '操作成功':
            return
        return 403

    def request(self, task: GdsCrawlerTask, method: str, url: str, params: dict = None, data: dict = None,
                json: dict = None, headers=None, path: str = None, name: str = '', tojson=False) -> Union[dict, str]:
        for i in range(3):
            response = None
            try:
                response = task.session.request(**{'method': method, 'url': url, 'data': data, 'headers': self.headers if not headers else headers,
                                                   'verify': False, 'timeout': self.timeout, 'params': params, 'json': json})
                status = response.status_code

                if status == 403:
                    status_ = self.remove_403(task.session)
                    if status_:
                        self.login()
                    continue
                if status == 206:
                    logger.error(f'break 状态码：{response.status_code} res: {response.text}')
                    raise MyException('206:Failed To Get Valid Ip')
                if status in [429, 500]:
                    logger.warning(f'continue 状态码：{response.status_code} res: {response.text}')
                    time.sleep(1)
                    continue

                logger.info(f'{name} --> {response.status_code}')
                a = response.text.replace("\n", "").replace('\r', '').replace('\t', '')
                logger.info(f'{name} --> {a}')

                if tojson:
                    """
                    {"Code":4,"Msg":"BrandController调用方法出现异常!Decrypt 异常IP异常:**************","Data":null}(200)
                    {"Code":4,"Msg":"","Data":"参数解密异常"}(200)
                    您今日的查询次数达到上限，请明日再查看！(200)
                    您的操作过于频繁，请稍后再试(429)
                    Unknown Exception(500)
                    """
                    if re.findall(r'(次数|解密异常|异常IP)', response.text):
                        logger.warning(f'break 状态码：{response.status_code} res: {response.text}')
                        break
                    return response.json()
                return response.text
            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                logger.warning(f'continue exception: {e}')
            except Exception as e:
                status = response.status_code if response else "空"
                text = response.text if response else "空"
                logger.warning(f'continue 状态码：{status} res: {text} exception: {e}')

        raise MyException('接口连续失败')

    def get_brand_from_db(self, firm_id):
        brand_all = {}
        for brand in self.brand_dao.get_many(firm_id=firm_id):
            if brand['brand_id'] == 0:
                continue
            brand_all.update({
                f'{brand["cn_name"]}_id': brand['brand_id'],
                f'{brand["cn_name"]}_barcodes': [product['barcode'] for product in self.product_dao.get_many(brand_id=brand['brand_id'])],
                f'{brand["cn_name"]}': True
            })
        return brand_all

    def get_old_barcodes_from_db(self, firm_id):
        barcode_all = [product['barcode'] for product in self.product_dao.get_many(firm_id=firm_id)]
        return barcode_all

    async def get_authorization_for_phone(self, phone):
        """为指定手机号获取Authorization token"""
        account_info: dict = self.account_manager[phone]

        # 延迟创建锁
        if account_info['login_lock'] is None:
            account_info['login_lock'] = asyncio.Lock()

        async with account_info['login_lock']:
            # 如果已有有效token，直接返回
            if account_info['authorization']:
                return account_info['authorization']

            # 标记正在登录
            account_info['is_logging'] = True

            try:
                # 执行登录（在线程池中运行同步代码）
                loop = asyncio.get_event_loop()
                tokens = await loop.run_in_executor(None, gds_login, phone, "Ssjj5@0999")
                authorization = f"Bearer {tokens['access_token']}"

                # 保存token
                account_info['authorization'] = authorization
                account_info['last_login_time'] = time.time()

                logger.info(f"手机号 {phone} 登录成功")
                return authorization

            except Exception as e:
                logger.error(f"手机号 {phone} 登录失败: {e}")
                raise e
            finally:
                # 取消登录状态标记
                account_info['is_logging'] = False

    async def refresh_authorization_for_phone(self, phone):
        """为指定手机号刷新Authorization token"""
        account_info: dict = self.account_manager[phone]

        # 延迟创建锁
        if account_info['login_lock'] is None:
            account_info['login_lock'] = asyncio.Lock()

        async with account_info['login_lock']:
            account_info['is_logging'] = True

            try:
                loop = asyncio.get_event_loop()
                tokens = await loop.run_in_executor(None, gds_login, phone, "Ssjj5@0999")
                authorization = f"Bearer {tokens['access_token']}"

                account_info['authorization'] = authorization
                account_info['last_login_time'] = time.time()

                logger.info(f"手机号 {phone} token刷新成功")
                return authorization

            except Exception as e:
                logger.error(f"手机号 {phone} token刷新失败: {e}")
                raise e
            finally:
                account_info['is_logging'] = False

    def increment_search_count(self, phone):
        """增加指定手机号的搜索次数"""
        account_info = self.account_manager[phone]
        current_date = time.strftime('%Y-%m-%d')

        # 检查是否需要重置每日计数
        if account_info['last_reset_date'] != current_date:
            account_info['daily_search_count'] = 0
            account_info['last_reset_date'] = current_date

        # 增加计数
        account_info['search_count'] += 1
        account_info['daily_search_count'] += 1

        logger.info(f"手机号 {phone} 搜索次数: 总计{account_info['search_count']}, 今日{account_info['daily_search_count']}")

    def get_search_statistics(self):
        """获取所有账号的搜索统计信息"""
        stats = {}
        for phone, account_info in self.account_manager.items():
            current_date = time.strftime('%Y-%m-%d')

            # 检查是否需要重置每日计数
            if account_info['last_reset_date'] != current_date:
                account_info['daily_search_count'] = 0
                account_info['last_reset_date'] = current_date

            stats[phone] = {
                'total_searches': account_info['search_count'],
                'daily_searches': account_info['daily_search_count'],
                'last_login_time': account_info['last_login_time'],
                'is_logging': account_info['is_logging']
            }

        return stats

    def get_least_used_phone(self):
        """获取搜索次数最少的可用手机号"""
        available_phones = []

        for phone in self.phones:
            account_info = self.account_manager[phone]
            if not account_info['is_logging']:
                available_phones.append((phone, account_info['search_count']))

        if not available_phones:
            return self.phones[random.randint(0, len(self.phones) - 1)]

        # 返回搜索次数最少的手机号
        return min(available_phones, key=lambda x: x[1])[0]
