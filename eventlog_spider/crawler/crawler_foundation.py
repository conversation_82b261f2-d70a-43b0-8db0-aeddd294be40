import json
from urllib.parse import quote
import re
from bs4 import BeautifulSoup
from eventlog_spider.common.eventlog_unify import Eventlog, StatusCode
from eventlog_spider.crawler.crawler import Crawler, CrawlerTask, MyException, CrawlerTools
from resx.func import cur_ts_sec
from resx.config import *
from resx.redis_types import RedisQueue
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class FoundationCrawlerTask(CrawlerTask):
    def __init__(self, eventlog: Eventlog):
        super().__init__(eventlog)

        if hasattr(eventlog, 'code') and hasattr(eventlog, 'crawler'):
            eventlog.crawler.receive_time = str(cur_ts_sec())


class FoundationCrawler(Crawler, CrawlerTools):
    @classmethod
    def get_name(cls):
        return 'foundation'

    def __init__(self, **kwargs):
        self.headers = {
            "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36"
        }
        self.url_search = "https://www.foundationcenter.org.cn/SelectData/GetSearchData"
        self.url_project = "https://www.foundationcenter.org.cn/SelectData/GetProjectData"
        self.url_finance = "https://www.foundationcenter.org.cn/SelectData/GetFinanceEchartsData"
        self.url_supervisor = "https://www.foundationcenter.org.cn/SelectData/GetSupervisorInfo"
        self.url_councils = "https://www.foundationcenter.org.cn/SelectData/GetCouInfo"
        self.url_index = "https://www.foundationcenter.org.cn/Content/Index"
        self.timeout = 2
        super().__init__(input_queue=RedisQueue(name='foundation_new', **CFG_REDIS_GS, db=9), task_cls=FoundationCrawlerTask,
                         eventlog_class=Eventlog, **kwargs)

    def do_crawl(self):
        task: FoundationCrawlerTask = self.get_crawler_task()
        eventlog: Eventlog = task.eventlog

        task.pages = {}
        if task.keyword is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.code = StatusCode.GIVE_UP
            return

        try:
            task.pages = self.crawl_(task)
            eventlog.code = StatusCode.SUCCESS
        except MyException as e:
            if e.message == '搜索为空':
                logger.warning(f'{task.keyword} -->搜索为空')
                eventlog.code = StatusCode.SEARCH_EMPTY_NO_RETRY
            if e.message == '接口连续失败':
                logger.warning(f'{task.keyword} -->接口连续失败')
                eventlog.code = StatusCode.GENERAL_ERROR_RETRY
        except Exception as e:
            raise e

    def crawl_(self, task: FoundationCrawlerTask):
        pages = dict()
        params = self.search(task)
        base_info, year = self.get_base_info(task, params['bh'])
        councils, supervisor, finance, project = [], [], {}, {}
        if year:
            councils = self.get_councils(task, params['bhv'], annualv=year)
            supervisor = self.get_supervisor(task, params['bhv'], annualv=year)
            finance = self.get_finance(task, params['bhv'])
            project = self.get_project(task, params['bhv'], annualv=int(year))

        if re.fullmatch(r'[A-Z0-9]{18}', task.keyword) and base_info.get('信用代码', '') != task.keyword:
            raise MyException('搜索为空')
        elif re.fullmatch(r'[\u4e00-\u9fff]+', task.keyword) and base_info.get('name', '') != task.keyword:
            raise MyException('搜索为空')

        pages['base_info.json'] = json.dumps(base_info, ensure_ascii=False)
        pages['councils.json'] = json.dumps(councils, ensure_ascii=False)
        pages['supervisor.json'] = json.dumps(supervisor, ensure_ascii=False)
        pages['finance.json'] = json.dumps(finance, ensure_ascii=False)
        pages['project.json'] = json.dumps(project, ensure_ascii=False)
        return pages

    def get_base_info(self, task: FoundationCrawlerTask, bh):
        def base_info_parse(html):
            soup = BeautifulSoup(html, 'lxml')
            divs = soup.select('.jb-001')
            zip1 = []
            zip2 = []
            for div in divs:
                strs = list(div.stripped_strings)
                zip1.append(strs[0])
                zip2.append(strs[1] if len(strs) > 1 else '')
            return dict(zip(zip1, zip2))

        def get_score(html):
            spans = BeautifulSoup(html, 'lxml').select('.sj-01.sj-02 span')
            if spans:
                year = spans[0].text
                score = spans[1].text
                return [year, score]
            return []

        def get_newest_year(html):
            # years = BeautifulSoup(html, 'lxml').select('span font')
            years = BeautifulSoup(html, 'lxml').select('a.active')
            if years:
                return years[0].text[:4]

        response = self.request(task, "GET", self.url_index, params={"bh": bh}, name='base_info')
        result = base_info_parse(response)
        # result['score'] = get_score(response)
        result['name'] = BeautifulSoup(response, 'lxml').select('.address span')[0].text
        logger.info(result)
        return result, get_newest_year(response)

    def get_councils(self, task: FoundationCrawlerTask, bhv, annualv='2022'):
        data = {"annualv": annualv, "bhv": bhv}
        response = self.request(task, "POST", self.url_councils, data=data, name='councils')
        soup = BeautifulSoup(response, 'lxml')
        result = list(map(lambda x: x.text, soup.select('span')))
        logger.info(result)
        return result

    def get_supervisor(self, task: FoundationCrawlerTask, bhv, annualv='2022'):
        data = {"annualv": annualv, "bhv": bhv}
        response = self.request(task, "POST", self.url_supervisor, data=data, name='supervisor')
        soup = BeautifulSoup(response, 'lxml')
        result = list(map(lambda x: x.text, soup.select('span')))
        logger.info(result)
        return result

    def get_finance(self, task: FoundationCrawlerTask, bhv):
        data = self.request(task, "POST", self.url_finance, data={"bhv": bhv}, tojson=True, name='finance')
        length = len(data['annual'])
        result = {}
        if length != len(data['jzc']) or length != len(data['jzsr']) or length != len(data['gyzc']) or length != len(data['tzsr']):
            for year in data['annual']:
                finance = self.parse_financeByYear(task, year, bhv)
                result[year] = {"净资产": finance[0] if finance[0] != '--' else '',
                                "捐赠收入": finance[1] if finance[1] != '--' else '',
                                "公益支出": finance[2] if finance[2] != '--' else '',
                                "年度总收入": finance[3] if finance[3] != '--' else ''}
        else:
            for idx, annual in enumerate(data['annual']):
                result[annual] = {"净资产": data['jzc'][idx],
                                  "捐赠收入": data['jzsr'][idx],
                                  "公益支出": data['gyzc'][idx],
                                  "年度总收入": data['tzsr'][idx]}
        logger.info(result)
        return result

    def parse_financeByYear(self, task, year, bhv):
        url = "https://www.foundationcenter.org.cn/SelectData/GetFinanceData"
        data = {"annualv": year, "bhv": bhv}
        response = self.request(task, "POST", url, data=data, name=f'financeByYear-{year}')
        spans = BeautifulSoup(response, 'lxml').select('span')
        result = list(map(lambda x: x.text.strip(), spans))
        logger.info(result)
        return result

    def get_project(self, task: FoundationCrawlerTask, bhv, annualv: int):
        """
        [项目名称,年度收入,年度支出,项目简介]
        """

        def parse_project(html):
            project = []
            for tr in BeautifulSoup(html, 'lxml').find_all('tr'):
                temp = []
                for idx, td in enumerate(tr.find_all('td')[1:]):
                    temp.append(td.text) if idx != 3 else temp.append(td['title'])
                project.append(temp)
            return project

        result = {}
        for _ in range(3):
            if _:
                annualv -= 1
            data = {"annualv": str(annualv), "bhv": bhv, "xmbhv": "0", "selectxmannual": "0"}
            response = self.request(task, 'POST', self.url_project, data=data, name=f'project-{annualv}')
            result[str(annualv)] = parse_project(response)
        logger.info(result)
        return result

    def search(self, task: FoundationCrawlerTask):
        data = quote(str({"KeyWords": f"{task.keyword},,", "SearchType": "allStr", "flag": "1",
                          "PageModel": {"PageSize": 15, "PageIndex": 1, "SortOrder": "asc"}}))
        response = self.request(task, "POST", self.url_search, data=data, name='search1')
        url = re.findall(r'<a href="(.*?)" target="_blank"><em>', response)[0]
        bh = re.findall(r'bh=(.*?)&', url)[0]
        response = self.request(task, "GET", f'https://www.foundationcenter.org.cn{url}', name='search2')
        bhv = BeautifulSoup(response, 'lxml').find(id="bh")['value']
        return {"bh": bh, "bhv": bhv}
