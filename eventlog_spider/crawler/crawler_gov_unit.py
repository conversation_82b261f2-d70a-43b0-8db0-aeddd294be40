import re
import time
import ddddocr
from bs4 import BeautifulSoup
from requests import Session
import json
from eventlog_spider.common.eventlog_unify import Eventlog, StatusCode
from eventlog_spider.crawler.crawler import Crawler, CrawlerTask, MyException, CrawlerTools
from resx.func import cur_ts_sec
from resx.config import *
from resx.redis_types import RedisQueue, Redis
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class GovUnitTask(CrawlerTask):
    def __init__(self, eventlog: Eventlog):
        super().__init__(eventlog)

        if hasattr(eventlog, 'code') and hasattr(eventlog, 'crawler'):
            eventlog.crawler.receive_time = str(cur_ts_sec())


class GovUnitCrawler(Crawler, CrawlerTools):

    @classmethod
    def get_name(cls):
        return 'gov_unit'

    def __init__(self, **kwargs):
        self.timeout = 5
        self.ocr = ddddocr.DdddOcr(show_ad=False)
        self.search_url = 'http://search.gjsy.gov.cn/wsss/query'
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'
        }
        self.redis_client = Redis(host='redis-2b0f46e5-545a-4c21-a4a4-053daff1ee7b.cn-north-4.dcs.myhuaweicloud.com', password="rdsnhgot0akp17Xq", db=0)
        super().__init__(input_queue=RedisQueue(name='gov_unit', **CFG_REDIS_GS, db=9), task_cls=GovUnitTask, eventlog_class=Eventlog, **kwargs)

    def do_crawl(self):
        task: GovUnitTask = self.get_crawler_task()
        eventlog: Eventlog = task.eventlog

        task.pages = {}
        if task.keyword is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.code = StatusCode.GIVE_UP
            return

        try:
            task.pages = self.crawl_(task)
            eventlog.code = StatusCode.SUCCESS
        except MyException as e:
            if e.message == '搜索为空':
                logger.warning(f'{task.keyword} -->搜索为空')
                eventlog.code = StatusCode.SEARCH_EMPTY_NO_RETRY
            if '连续失败' in e.message:
                logger.warning(f'连续失败 {eventlog}')
                eventlog.code = StatusCode.GENERAL_ERROR_RETRY
        except Exception as e:
            raise e

    def crawl_(self, task: GovUnitTask):
        session = self.get_new_session()

        html = self.search(session, task.keyword)
        base_info = self.parse(task.keyword, html)
        token = re.findall(r'a1.push\("/annual/random/(.*?)"\);', html)
        changes = re.findall(r'a1.push\("/bulletin/random/(.*?)"\);', html)

        report_info = {}
        change_info = []
        if token:
            report_html, report_url = self.report_change(session, token[0], 'annual')
            report_info = self.parse_report(report_html, report_url)
        if changes:
            for change in changes:
                change_html, _ = self.report_change(session, change, 'bulletin')
                change_info.extend(self.parse_change(base_info.get('统一社会信用代码', ''), change_html))

        return {
            'gov_unit': json.dumps(base_info, ensure_ascii=False),
            'gov_unit_report': json.dumps(report_info, ensure_ascii=False),
            'gov_unit_change': json.dumps(change_info, ensure_ascii=False)
        }

    def search(self, session: Session, keyword):
        for _ in range(10):
            url = f"http://search.gjsy.gov.cn/randomcode/refresh/{int(time.time() * 1000)}"
            response = self.request(session, 'GET', url, name='captcha1', toRaw=True)

            captcha = self.ocr.classification(response.content)
            data = {"ts": "", "c": "100000", "t": "2", "s": keyword, "random": captcha}
            text = self.request(session, 'POST', self.search_url, data=data, name='search')
            if '您所查询的单位不存在' in text:
                raise MyException('搜索为空')
            if re.search(r'(验证码不匹配|验证码输入错误)', text):
                continue
            return text
        raise MyException('验证码连续失败')

    def report_change(self, session: Session, token: str, type_: str):
        """
        report: annual
        change: bulletin
        """
        for _ in range(3):
            url = f"http://search.gjsy.gov.cn/{type_}/random/{token}"
            url2 = f"http://search.gjsy.gov.cn/randomcode/refresh/{int(time.time() * 1000)}"
            url3 = f"http://search.gjsy.gov.cn/{type_}/{'report' if type_ == 'annual' else 'detail'}/{token}"

            self.request(session, 'GET', url, name='report-first', toRaw=True)
            response = self.request(session, 'GET', url2, name='captcha2', toRaw=True)

            captcha = self.ocr.classification(response.content)
            text = self.request(session, 'POST', url3, data={"random": captcha}, name='report')
            if re.search(r'(验证码不匹配|验证码输入错误)', text):
                continue
            return text, url
        raise MyException('验证码连续失败')

    def parse(self, us_credit_code, html):
        try:
            soup = BeautifulSoup(html, 'lxml')
            trs = soup.select('table[width="98%"]')[0].find_all('tr')
            keys, values = [], []
            for tr in trs:
                list_ = list(tr.stripped_strings)
                if list_ and len(list_) == 2:
                    keys.append(list_[0])
                    values.append(re.sub(r'[\n\s\t\r]', '', list_[1]))
            data = dict(zip(keys, values))
            logger.info(data)
            return data
        except Exception as e:
            logger.error(f'基本信息解析失败 {us_credit_code} {self.custom_traceback(e)}')
            self.redis_client.rpush('gov_unit_parse_base_error', us_credit_code)
            raise MyException('基本信息解析失败')

    def parse_report(self, html, report_url):
        us_credit_code = ''
        try:
            soup = BeautifulSoup(html, 'lxml')
            us_credit_code_and_name = soup.find(string=lambda text: text and re.search(r'[A-Z0-9]{18}(-|- | -)[\u4e00-\u9fa5]+', text)).split('-')
            us_credit_code, name = us_credit_code_and_name[0].strip(), us_credit_code_and_name[1].strip()
            pub_time = soup.find(string=lambda text: text and re.search(r'时间：\d{4}年\d{2}月\d{2}日', text)).strip().replace('时间：', '')
            year = soup.select('span[lang="EN-US"][style="font-size: 15pt;"]')[1].text

            trs = soup.select('table.MsoNormalTable tbody')[2].find_all('tr')
            keys, values = [], []
            for idx, tr in enumerate(trs[1:-1]):
                tds = [''.join(td.stripped_strings) for td in tr.find_all('td')]
                if idx < 6 or idx > 9:
                    keys.append(tds[0])
                    values.append(tds[1])
                if idx == 7:
                    keys.extend(['资产损益情况 净资产合计（所有者权益合计）' + td for td in tds])
                if idx == 8:
                    values.extend([float(td) for td in tds])
                if idx == 9:
                    keys.extend([tds[0], tds[2]])
                    values.extend([tds[1], tds[3]])
            data = dict(zip(keys, values))
            data.update({'us_credit_code': us_credit_code, 'name': name, 'pub_time': pub_time, 'year': year, 'source': report_url})
            logger.info(data)
            return data
        except Exception as e:
            logger.error(f'报告解析失败 {us_credit_code} {self.custom_traceback(e)}')
            self.redis_client.rpush('gov_unit_parse_report_error', us_credit_code)
            return {}

    def parse_change(self, us_credit_code, html):
        try:
            changes = []
            soup = BeautifulSoup(html, 'lxml')
            date = soup.find(string=lambda text: text and re.search(r'时间：\d{4}年\d{2}月\d{2}日', text))
            if not date:
                return []
            change_time = date.strip().replace('时间：', '')
            change_items = [str(i).replace('的变更', '') for i in soup.find_all(string=re.compile(r'^(.*?)的变更$'))]
            after_befores = [str(j).replace('）', '').split('（原：') for j in soup.find_all(string=re.compile(r'^(.*?)（原：(.*?)）$'))]
            for idx, change_item in enumerate(change_items):
                change_after, change_before = after_befores[idx]
                changes.append({'change_item': change_item, 'change_before': change_before, 'change_after': change_after, 'change_time': change_time})
            return changes
        except Exception as e:
            logger.error(f'变更解析失败 {us_credit_code} {self.custom_traceback(e)}')
            self.redis_client.rpush('gov_unit_parse_change_error', us_credit_code)
            return []
